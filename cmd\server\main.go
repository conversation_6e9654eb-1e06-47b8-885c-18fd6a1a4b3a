package main

import (
	"log"
	"webapp/configs"
	"webapp/internal/auth"
	"webapp/internal/database"
	"webapp/internal/handlers"
	"webapp/internal/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	config := configs.LoadConfig()

	// Initialize database
	database.InitDatabase()

	// Initialize services
	authService := auth.NewAuthService(database.GetDB(), config.JWTSecret)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)

	// Initialize Gin router
	router := gin.Default()

	// CORS middleware
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		AllowCredentials: true,
	}))

	// Custom CORS middleware for additional control
	router.Use(middleware.CORSMiddleware())

	// Serve static files
	router.Static("/static", "./web/static")
	router.LoadHTMLGlob("web/templates/*")

	// Web routes (serve HTML pages)
	router.GET("/", func(c *gin.Context) {
		c.HTML(200, "index.html", gin.H{
			"title": "Home Page",
		})
	})

	router.GET("/auth/login", func(c *gin.Context) {
		c.HTML(200, "login.html", gin.H{
			"title": "Login",
		})
	})

	router.GET("/auth/register", func(c *gin.Context) {
		c.HTML(200, "register.html", gin.H{
			"title": "Register",
		})
	})

	router.GET("/auth/forgot-password", func(c *gin.Context) {
		c.HTML(200, "forgot-password.html", gin.H{
			"title": "Forgot Password",
		})
	})

	router.GET("/dashboard", func(c *gin.Context) {
		c.HTML(200, "dashboard.html", gin.H{
			"title": "Dashboard",
		})
	})

	// API routes
	api := router.Group("/api")
	{
		// Authentication routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/register", authHandler.Register)
			auth.POST("/forgot-password", authHandler.ForgotPassword)
			auth.POST("/verify-token", authHandler.VerifyToken)
		}

		// Protected routes
		protected := api.Group("/user")
		protected.Use(middleware.AuthMiddleware(authService))
		{
			protected.GET("/profile", authHandler.GetProfile)
		}
	}

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Server is running",
		})
	})

	log.Printf("Server starting on port %s", config.Port)
	log.Fatal(router.Run(":" + config.Port))
}
