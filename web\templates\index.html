{{define "content"}}
<div class="d-flex flex-column flex-column-fluid bgi-position-y-bottom position-x-center bgi-no-repeat bgi-size-contain bgi-attachment-fixed"
     style="background-image: url('/static/media/illustrations/sketchy-1/14.png');">
    
    <!-- Content -->
    <div class="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20">
        <!-- Logo -->
        <a href="/" class="mb-12">
            <img alt="Logo" src="/static/media/logos/default.svg" class="h-45px" />
        </a>
        
        <!-- Wrapper -->
        <div class="w-lg-600px bg-white rounded shadow-sm p-10 p-lg-15 mx-auto text-center">
            <h1 class="text-dark mb-3">Welcome to WebApp</h1>
            <p class="text-gray-400 fw-bold fs-4 mb-10">
                A modern web application built with Golang and beautiful UI components.
            </p>
            
            <!-- Features -->
            <div class="row g-5 mb-10">
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="symbol symbol-50px symbol-circle bg-light-primary mb-5 mx-auto">
                            <span class="svg-icon svg-icon-2x svg-icon-primary">🔐</span>
                        </div>
                        <h5 class="fw-bolder text-gray-800">Secure Authentication</h5>
                        <p class="text-gray-600 fw-bold fs-6">JWT-based authentication with bcrypt password hashing</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="symbol symbol-50px symbol-circle bg-light-success mb-5 mx-auto">
                            <span class="svg-icon svg-icon-2x svg-icon-success">⚡</span>
                        </div>
                        <h5 class="fw-bolder text-gray-800">Fast Performance</h5>
                        <p class="text-gray-600 fw-bold fs-6">Built with Golang for high performance and scalability</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="symbol symbol-50px symbol-circle bg-light-info mb-5 mx-auto">
                            <span class="svg-icon svg-icon-2x svg-icon-info">🎨</span>
                        </div>
                        <h5 class="fw-bolder text-gray-800">Modern UI</h5>
                        <p class="text-gray-600 fw-bold fs-6">Beautiful and responsive user interface design</p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="d-flex flex-center flex-wrap">
                <a href="/auth/login" class="btn btn-lg btn-primary me-4 mb-3">
                    Sign In
                </a>
                <a href="/auth/register" class="btn btn-lg btn-light-primary mb-3">
                    Create Account
                </a>
            </div>
            
            <!-- Demo Info -->
            <div class="mt-10 p-8 bg-light-info rounded">
                <div class="text-info">
                    <strong>Demo Account:</strong><br>
                    Email: <strong><EMAIL></strong><br>
                    Password: <strong>demo</strong>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div class="d-flex flex-center flex-column-auto p-10">
        <div class="d-flex align-items-center fw-bold fs-6">
            <a href="#" class="text-muted text-hover-primary px-2">About</a>
            <a href="#" class="text-muted text-hover-primary px-2">Contact</a>
            <a href="#" class="text-muted text-hover-primary px-2">Documentation</a>
        </div>
    </div>
</div>
{{end}}

{{template "base.html" .}}
