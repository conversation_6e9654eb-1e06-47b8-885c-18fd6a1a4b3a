package configs

import (
	"os"
)

type Config struct {
	Port      string
	JWTSecret string
	DBPath    string
}

// LoadConfig loads configuration from environment variables
func LoadConfig() *Config {
	config := &Config{
		Port:      getEnv("PORT", "8080"),
		JWTSecret: getEnv("JWT_SECRET", "your-secret-key-change-this-in-production"),
		DBPath:    getEnv("DB_PATH", "webapp.db"),
	}

	return config
}

// getEnv gets environment variable with fallback
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
