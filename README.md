# Golang Web Application

## Cấu trúc dự án

```
├── cmd/
│   └── server/          # Entry point của ứng dụng
├── internal/
│   ├── auth/           # Authentication logic
│   ├── handlers/       # HTTP handlers
│   ├── middleware/     # Middleware functions
│   ├── models/         # Database models
│   └── database/       # Database connection
├── pkg/
│   └── utils/          # Utility functions
├── web/
│   ├── static/         # Static files (CSS, JS, images)
│   └── templates/      # HTML templates
├── configs/            # Configuration files
├── migrations/         # Database migrations
└── docs/              # Documentation
```

## Chức năng

- Đăng nhập/Đăng ký
- Quên mật khẩu
- Dashboard người dùng
- JWT Authentication
- RESTful API

## Công nghệ sử dụng

- Go 1.21+
- Gin Web Framework
- GORM (ORM)
- JWT-Go
- BCrypt
- SQLite/PostgreSQL

## Cài đặt và chạy

```bash
# Clone repository
git clone <repository-url>
cd <project-name>

# Cài đặt dependencies
go mod tidy

# Chạy ứng dụng
go run cmd/server/main.go
```

## API Endpoints

### Authentication
- POST /api/auth/login - Đăng nhập
- POST /api/auth/register - Đăng ký
- POST /api/auth/forgot-password - Quên mật khẩu
- POST /api/auth/verify-token - Xác thực token

### Protected Routes
- GET /api/user/profile - Lấy thông tin user
- PUT /api/user/profile - Cập nhật thông tin user
