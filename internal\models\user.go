package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID                uint           `json:"id" gorm:"primaryKey"`
	FirstName         string         `json:"first_name" gorm:"size:100;not null"`
	LastName          string         `json:"last_name" gorm:"size:100;not null"`
	Email             string         `json:"email" gorm:"uniqueIndex;size:255;not null"`
	Password          string         `json:"-" gorm:"size:255;not null"` // "-" means exclude from JSON
	EmailVerifiedAt   *time.Time     `json:"email_verified_at"`
	ResetToken        string         `json:"-" gorm:"size:255"`
	ResetTokenExpiry  *time.Time     `json:"-"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`
}

// UserResponse represents user data for API responses (without sensitive fields)
type UserResponse struct {
	ID              uint       `json:"id"`
	FirstName       string     `json:"first_name"`
	LastName        string     `json:"last_name"`
	Email           string     `json:"email"`
	EmailVerifiedAt *time.Time `json:"email_verified_at"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// ToResponse converts User to UserResponse
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:              u.ID,
		FirstName:       u.FirstName,
		LastName:        u.LastName,
		Email:           u.Email,
		EmailVerifiedAt: u.EmailVerifiedAt,
		CreatedAt:       u.CreatedAt,
		UpdatedAt:       u.UpdatedAt,
	}
}

// LoginRequest represents login request payload
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=3"`
}

// RegisterRequest represents registration request payload
type RegisterRequest struct {
	FirstName string `json:"firstname" binding:"required,min=2"`
	LastName  string `json:"lastname" binding:"required,min=2"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
}

// ForgotPasswordRequest represents forgot password request payload
type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// AuthResponse represents authentication response
type AuthResponse struct {
	User     UserResponse `json:"user"`
	APIToken string       `json:"api_token"`
}
