// Authentication JavaScript
class AuthManager {
    constructor() {
        this.apiUrl = '/api/auth';
        this.token = localStorage.getItem('auth_token');
        this.init();
    }

    init() {
        // Check if user is logged in and redirect accordingly
        if (this.token && window.location.pathname.startsWith('/auth/')) {
            this.verifyToken().then(valid => {
                if (valid) {
                    window.location.href = '/dashboard';
                }
            });
        }

        // Initialize form handlers
        this.initLoginForm();
        this.initRegisterForm();
        this.initForgotPasswordForm();
        this.initDashboard();
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (this.token) {
            options.headers['Authorization'] = `Bearer ${this.token}`;
        }

        if (data) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(endpoint, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || 'Request failed');
            }
            
            return result;
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }

    async verifyToken() {
        if (!this.token) return false;
        
        try {
            await this.apiCall(`${this.apiUrl}/verify-token`, 'POST');
            return true;
        } catch (error) {
            this.logout();
            return false;
        }
    }

    initLoginForm() {
        const form = document.getElementById('kt_login_signin_form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('kt_sign_in_submit');
            const indicator = submitBtn.querySelector('.indicator-progress');
            const label = submitBtn.querySelector('.indicator-label');
            
            // Show loading
            submitBtn.disabled = true;
            indicator.style.display = 'block';
            label.style.display = 'none';

            try {
                const formData = new FormData(form);
                const data = {
                    email: formData.get('email'),
                    password: formData.get('password')
                };

                const result = await this.apiCall(`${this.apiUrl}/login`, 'POST', data);
                
                // Store token
                this.token = result.api_token;
                localStorage.setItem('auth_token', this.token);
                
                // Redirect to dashboard
                window.location.href = '/dashboard';
                
            } catch (error) {
                this.showAlert('alert-container', error.message, 'danger');
            } finally {
                // Hide loading
                submitBtn.disabled = false;
                indicator.style.display = 'none';
                label.style.display = 'block';
            }
        });
    }

    initRegisterForm() {
        const form = document.getElementById('kt_login_signup_form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('kt_sign_up_submit');
            const indicator = submitBtn.querySelector('.indicator-progress');
            const label = submitBtn.querySelector('.indicator-label');
            
            // Show loading
            submitBtn.disabled = true;
            indicator.style.display = 'block';
            label.style.display = 'none';

            try {
                const formData = new FormData(form);
                const data = {
                    firstname: formData.get('firstname'),
                    lastname: formData.get('lastname'),
                    email: formData.get('email'),
                    password: formData.get('password')
                };

                // Validate terms
                if (!formData.get('acceptTerms')) {
                    throw new Error('Please accept terms and conditions');
                }

                const result = await this.apiCall(`${this.apiUrl}/register`, 'POST', data);
                
                // Store token
                this.token = result.api_token;
                localStorage.setItem('auth_token', this.token);
                
                // Redirect to dashboard
                window.location.href = '/dashboard';
                
            } catch (error) {
                this.showAlert('alert-container', error.message, 'danger');
            } finally {
                // Hide loading
                submitBtn.disabled = false;
                indicator.style.display = 'none';
                label.style.display = 'block';
            }
        });
    }

    initForgotPasswordForm() {
        const form = document.getElementById('kt_login_password_reset_form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('kt_password_reset_submit');
            const indicator = submitBtn.querySelector('.indicator-progress');
            const label = submitBtn.querySelector('.indicator-label');
            
            // Show loading
            submitBtn.disabled = true;
            indicator.style.display = 'block';
            label.style.display = 'none';

            try {
                const formData = new FormData(form);
                const data = {
                    email: formData.get('email')
                };

                await this.apiCall(`${this.apiUrl}/forgot-password`, 'POST', data);
                
                // Show success message
                document.getElementById('alert-error').style.display = 'none';
                document.getElementById('alert-success').style.display = 'block';
                
            } catch (error) {
                document.getElementById('alert-success').style.display = 'none';
                document.getElementById('alert-error').style.display = 'block';
            } finally {
                // Hide loading
                submitBtn.disabled = false;
                indicator.style.display = 'none';
                label.style.display = 'block';
            }
        });
    }

    async initDashboard() {
        // Only run on dashboard page
        if (!window.location.pathname.includes('/dashboard')) return;
        
        // Verify authentication
        if (!this.token) {
            window.location.href = '/auth/login';
            return;
        }

        try {
            // Get user profile
            const user = await this.apiCall('/api/user/profile');
            
            // Update UI with user data
            this.updateDashboardUI(user);
            
            // Initialize logout handler
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.logout();
                });
            }
            
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            this.logout();
        }
    }

    updateDashboardUI(user) {
        // Update user name and email
        const userName = document.getElementById('user-name');
        const userEmail = document.getElementById('user-email');
        const welcomeName = document.getElementById('welcome-name');
        const welcomeEmail = document.getElementById('welcome-email');
        const memberSince = document.getElementById('member-since');

        if (userName) userName.textContent = `${user.first_name} ${user.last_name}`;
        if (userEmail) userEmail.textContent = user.email;
        if (welcomeName) welcomeName.textContent = `Welcome, ${user.first_name}!`;
        if (welcomeEmail) welcomeEmail.textContent = user.email;
        if (memberSince) {
            const year = new Date(user.created_at).getFullYear();
            memberSince.textContent = year;
        }

        // Update stats (mock data for now)
        const totalUsers = document.getElementById('total-users');
        const activeSessions = document.getElementById('active-sessions');
        const totalLogins = document.getElementById('total-logins');
        
        if (totalUsers) totalUsers.textContent = '1,234';
        if (activeSessions) activeSessions.textContent = '56';
        if (totalLogins) totalLogins.textContent = '8,945';
    }

    showAlert(containerId, message, type = 'danger') {
        const container = document.getElementById(containerId);
        const alertText = document.getElementById('alert-text');
        
        if (container && alertText) {
            alertText.textContent = message;
            container.style.display = 'block';
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }
    }

    logout() {
        localStorage.removeItem('auth_token');
        this.token = null;
        window.location.href = '/auth/login';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AuthManager();
});
